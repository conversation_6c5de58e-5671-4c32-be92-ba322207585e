import Swal from "sweetalert2";

document.addEventListener("DOMContentLoaded", function () {
    // Elements
    const transactionsTableBody = document.getElementById(
        "transactions-table-body"
    );
    const paginationContainer = document.getElementById("pagination-container");
    const entriesInfo = document.getElementById("entries-info");
    const invoicedTransactionsTableBody = document.getElementById(
        "invoiced-transactions-table-body"
    );
    const invoiceStatusFilter = document.getElementById("invoice-status-filter");
    const invoiceSearchInput = document.getElementById("invoice-search-input");
    const invoicedPaginationContainer = document.getElementById("invoiced-pagination-container");
    const invoicedEntriesInfo = document.getElementById("invoiced-entries-info");
    const dateFrom = document.getElementById("date-from");
    const dateTo = document.getElementById("date-to");
    const siteFilter = document.getElementById("site-filter");
    const unitFilter = document.getElementById("unit-filter");
    const statusFilter = document.getElementById("status-filter");
    const searchInput = document.getElementById("search-input");
    const updateStatusForm = document.getElementById("update-status-form");
    const transactionIdInput = document.getElementById("transaction-id");
    const statusSelect = document.getElementById("status");
    const salesNotesTextarea = document.getElementById("sales_notes");
    const toggleStatusFormBtn = document.getElementById("btn-toggle-status-form");
    const closeStatusFormBtn = document.getElementById("close-status-form");
    const closeMainModalBtn = document.getElementById("btn-close-main-modal");
    const selectAllTransactionsCheckbox = document.getElementById("select-all-transactions");
    const generateInvoiceBtn = document.getElementById("generate-invoice-btn");
    const sortableHeaders = document.querySelectorAll(".sortable");

    // State variables
    let selectedTransactions = [];
    let currentSortField = "";
    let currentSortOrder = "asc";

    // Load transactions on page load
    loadTransactions();
    loadInvoicedTransactions();

    // Add event listeners for sortable headers
    sortableHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const sortField = this.getAttribute('data-sort');
            const table = this.closest('table').id;

            // Reset all headers
            document.querySelectorAll(`#${table} .sortable`).forEach(h => {
                h.classList.remove('asc', 'desc');
            });

            // Set sort order
            if (currentSortField === sortField) {
                // Toggle sort order if clicking the same header
                currentSortOrder = currentSortOrder === 'asc' ? 'desc' : 'asc';
            } else {
                // Default to ascending order for a new sort field
                currentSortField = sortField;
                currentSortOrder = 'asc';
            }

            // Add appropriate class to the clicked header
            this.classList.add(currentSortOrder);

            // Load data with the new sort parameters
            if (table === 'transactions-table') {
                loadTransactions(1, sortField, currentSortOrder);
            } else if (table === 'invoiced-transactions-table') {
                loadInvoicedTransactions(1, sortField, currentSortOrder);
            }
        });
    });

    // Add event listener for select all checkbox
    if (selectAllTransactionsCheckbox) {
        selectAllTransactionsCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.transaction-checkbox:not([disabled])');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;

                // Trigger the change event manually
                const event = new Event('change');
                checkbox.dispatchEvent(event);
            });
        });
    }

    // Add event listener for generate invoice button
    if (generateInvoiceBtn) {
        generateInvoiceBtn.addEventListener('click', function() {
            if (selectedTransactions.length === 0) {
                Swal.fire({
                    title: "Peringatan",
                    text: "Pilih minimal satu transaksi untuk membuat invoice",
                    icon: "warning",
                    confirmButtonText: "OK"
                });
                return;
            }

            // Check if selected transactions are already invoiced
            checkTransactionsInvoiced();
        });
    }

    // Add event listeners for filters
    dateFrom.addEventListener("change", () => {
        loadTransactions();
        loadInvoicedTransactions();
    });
    dateTo.addEventListener("change", () => {
        loadTransactions();
        loadInvoicedTransactions();
    });
    siteFilter.addEventListener("change", () => {
        loadUnits();
        loadTransactions();
        loadInvoicedTransactions();
    });
    unitFilter.addEventListener("change", () => {
        loadTransactions();
        loadInvoicedTransactions();
    });
    statusFilter.addEventListener("change", () => {
        loadTransactions();
        loadInvoicedTransactions();
    });
    searchInput.addEventListener(
        "input",
        debounce(() => {
            loadTransactions();
            loadInvoicedTransactions();
        }, 500)
    );

    // Add event listeners for invoice filters
    if (invoiceStatusFilter) {
        invoiceStatusFilter.addEventListener("change", () => {
            loadInvoicedTransactions();
        });
    }
    if (invoiceSearchInput) {
        invoiceSearchInput.addEventListener(
            "input",
            debounce(() => {
                loadInvoicedTransactions();
            }, 500)
        );
    }

    // Load units on page load
    loadUnits();

    // Load transactions and invoices on page load
    loadTransactions();
    loadInvoicedTransactions();

    // Add event listener for update status form
    if (updateStatusForm) {
        updateStatusForm.addEventListener("submit", function (e) {
            e.preventDefault();
            updateTransactionStatus();
        });
    }

    // Add event listener for close status form button
    if (closeStatusFormBtn) {
        closeStatusFormBtn.addEventListener("click", function() {
            closeStatusUpdateForm();
        });
    }

    // Add event listener for close main modal button
    if (closeMainModalBtn) {
        closeMainModalBtn.addEventListener("click", function() {
            // Use jQuery to close the modal (more reliable across Bootstrap versions)
            $('#transaction-details-modal').modal('hide');
        });
    }

    // Add event listener for save invoice button
    const saveInvoiceBtn = document.getElementById('save-invoice-btn');
    if (saveInvoiceBtn) {
        saveInvoiceBtn.addEventListener('click', function() {
            saveInvoice();
        });
    }

    // Add event listener for generate next invoice number button
    document.addEventListener('click', function(e) {
        if (e.target && e.target.id === 'generate-next-invoice-number') {
            const currentInvoiceNumber = document.getElementById('invoice-no').value;
            if (currentInvoiceNumber && currentInvoiceNumber.includes('/')) {
                // Try to generate the next invoice number by incrementing the first part
                const nextInvoiceNumber = getNextInvoiceNumber(currentInvoiceNumber);
                if (nextInvoiceNumber) {
                    document.getElementById('invoice-no').value = nextInvoiceNumber;
                    // No notification needed when updating the invoice number
                    return;
                }
            }

            // If we can't parse the current number or it doesn't exist, generate a new one based on current date
            // Fetch the latest invoice to get the next number
            fetch('/sales/latest-invoice')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.latest_invoice) {
                        // Use the exact format from the last invoice
                        const lastInvoiceNumber = data.latest_invoice.no_invoice;
                        const nextInvoiceNumber = getNextInvoiceNumber(lastInvoiceNumber);

                        if (nextInvoiceNumber) {
                            document.getElementById('invoice-no').value = nextInvoiceNumber;
                            return;
                        }
                    }

                    // Fallback to default format if no valid last invoice or couldn't parse it
                    const today = new Date();
                    const month = today.getMonth() + 1; // JavaScript months are 0-based
                    const year = today.getFullYear();
                    document.getElementById('invoice-no').value = formatInvoiceNumber(1, month, year);

                    // No notification needed when creating a new invoice number
                })
                .catch(error => {
                    // Fallback to a simple format if fetch fails
                    const today = new Date();
                    const month = today.getMonth() + 1;
                    const year = today.getFullYear();
                    document.getElementById('invoice-no').value = formatInvoiceNumber(1, month, year);
                });
        }
    });

    // Add event listener for toggle status form button
    if (toggleStatusFormBtn) {
        toggleStatusFormBtn.addEventListener("click", function() {
            const statusUpdateContainer = document.getElementById('status-update-container');

            if (statusUpdateContainer.style.display === 'none') {
                // Show the form
                statusUpdateContainer.style.display = 'block';
                this.innerHTML = '<i class="mdi mdi-close"></i> Tutup Form';
                this.classList.remove('btn-light');
                this.classList.add('btn-danger');

                // Add overlay
                const overlay = document.createElement('div');
                overlay.id = 'status-form-overlay';
                overlay.className = 'modal-backdrop fade show';
                overlay.style.opacity = '0.5';
                overlay.style.zIndex = '1040';
                document.body.appendChild(overlay);

                // Add click event to close form when clicking outside
                overlay.addEventListener('click', function() {
                    statusUpdateContainer.style.display = 'none';
                    toggleStatusFormBtn.innerHTML = '<i class="mdi mdi-pencil"></i> Ubah Status';
                    toggleStatusFormBtn.classList.remove('btn-danger');
                    toggleStatusFormBtn.classList.add('btn-light');
                    document.body.removeChild(this);
                });
            } else {
                // Hide the form using the closeStatusUpdateForm function
                closeStatusUpdateForm();
            }
        });
    }

    // Add event listener for toggle notes form button
    const toggleNotesFormBtn = document.getElementById('btn-toggle-notes-form');
    if (toggleNotesFormBtn) {
        toggleNotesFormBtn.addEventListener("click", function() {
            const notesUpdateContainer = document.getElementById('notes-update-container');

            if (notesUpdateContainer.style.display === 'none') {
                // Show the form
                notesUpdateContainer.style.display = 'block';
                this.innerHTML = '<i class="mdi mdi-close"></i> Tutup Form';
                this.classList.remove('btn-info');
                this.classList.add('btn-danger');

                // Add overlay
                const overlay = document.createElement('div');
                overlay.id = 'notes-form-overlay';
                overlay.className = 'modal-backdrop fade show';
                overlay.style.opacity = '0.5';
                overlay.style.zIndex = '1040';
                document.body.appendChild(overlay);

                // Add click event to close form when clicking outside
                overlay.addEventListener('click', function() {
                    notesUpdateContainer.style.display = 'none';
                    toggleNotesFormBtn.innerHTML = '<i class="mdi mdi-note-edit"></i> Catatan Sales';
                    toggleNotesFormBtn.classList.remove('btn-danger');
                    toggleNotesFormBtn.classList.add('btn-info');
                    document.body.removeChild(this);
                });
            } else {
                // Hide the form
                closeNotesUpdateForm();
            }
        });
    }

    // Add event listener for close notes form button
    const closeNotesFormBtn = document.getElementById('close-notes-form');
    if (closeNotesFormBtn) {
        closeNotesFormBtn.addEventListener("click", function() {
            closeNotesUpdateForm();
        });
    }

    // Add event listener for update notes form
    const updateNotesForm = document.getElementById('update-notes-form');
    if (updateNotesForm) {
        updateNotesForm.addEventListener("submit", function(e) {
            e.preventDefault();
            updateTransactionNotes();
        });
    }

    // Function to load units based on selected site
    function loadUnits() {
        // Clear current options except the first one
        unitFilter.innerHTML = '<option value="">Semua Unit</option>';

        // If no site is selected, return early
        if (!siteFilter.value) {
            return;
        }

        // Fetch units for the selected site
        fetch(`/sales/units?site_id=${siteFilter.value}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Add units to the dropdown
                data.forEach(unit => {
                    const option = document.createElement('option');
                    option.value = unit.id;
                    option.textContent = `${unit.unit_code} - ${unit.unit_type}`;
                    unitFilter.appendChild(option);
                });
            })
            .catch(error => {
                Swal.fire({
                    title: "Error!",
                    text: "Gagal memuat data unit. Silakan coba lagi.",
                    icon: "error",
                    confirmButtonText: "OK",
                });
            });
    }

    // Function to load transactions with filters
    function loadTransactions(page = 1, sortField = "", sortOrder = "asc") {
        // Show loading indicator
        transactionsTableBody.innerHTML = '<tr><td colspan="9" class="text-center">Loading...</td></tr>';

        // Update global sort variables
        if (sortField) {
            currentSortField = sortField;
            currentSortOrder = sortOrder;
        }

        const params = new URLSearchParams({
            date_from: dateFrom.value,
            date_to: dateTo.value,
            site_id: siteFilter.value,
            unit_id: unitFilter.value,
            status: statusFilter.value,
            search: searchInput.value,
            page: page,
        });

        // Add sort parameters if available
        if (currentSortField) {
            params.append('sort_field', currentSortField);
            params.append('sort_order', currentSortOrder);
        }

        fetch(`/sales/transactions?${params}`)
            .then((response) => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then((data) => {
                renderTransactions(data);
                renderPagination(data);
                updateEntriesInfo(data);
            })
            .catch((error) => {
                Swal.fire({
                    title: "Error!",
                    text: "Gagal memuat data transaksi. Silakan coba lagi.",
                    icon: "error",
                    confirmButtonText: "OK",
                });
            });
    }

    // Function to render transactions in the table
    function renderTransactions(data) {
        transactionsTableBody.innerHTML = "";

        if (data.data.length === 0) {
            const emptyRow = document.createElement("tr");
            emptyRow.innerHTML =
                '<td colspan="10" class="text-center">Tidak ada data transaksi</td>';
            transactionsTableBody.appendChild(emptyRow);
            return;
        }

        data.data.forEach((transaction, index) => {
            const row = document.createElement("tr");

            // Format date
            const createdAt = new Date(transaction.created_at);
            const formattedDate = formatDate(createdAt);

            // Get unit information
            const unitInfo = transaction.unit
                ? `${transaction.unit.unit_code} - ${transaction.unit.unit_type}`
                : "N/A";

            // Create status badge
            const statusBadge = getStatusBadge(transaction.status);

            // Check if transaction is already invoiced
            const isInvoiced = transaction.invoices && transaction.invoices.length > 0;

            // Check if transaction is already selected
            const isSelected = selectedTransactions.some(t => t.id === transaction.id);

            row.innerHTML = `
                <td>
                    <div class="form-check">
                        <input class="form-check-input transaction-checkbox" type="checkbox"
                            value="${transaction.id}"
                            data-transaction='${JSON.stringify({
                                id: transaction.id,
                                unit_code: transaction.unit ? transaction.unit.unit_code : 'N/A',
                                unit_type: transaction.unit ? transaction.unit.unit_type : 'N/A',
                                site_name: transaction.site ? transaction.site.site_name : 'N/A'
                            })}'
                            ${isSelected ? 'checked' : ''}
                            ${isInvoiced ? 'disabled' : ''}>
                    </div>
                </td>
                <td>${(data.current_page - 1) * data.per_page + index + 1}</td>
                <td>${
                    transaction.site
                        ? transaction.site.site_name
                        : transaction.site_id
                }</td>
                <td>${unitInfo}</td>
                <td>${statusBadge}</td>
                <td>${transaction.wo_number || "-"}</td>
                <td>${transaction.po_number || "-"}</td>
                <td>${transaction.site && transaction.site.site_id === 'IMK' ? (transaction.noSPB || "-") : (transaction.do_number || "-")}</td>
                <td>${formattedDate}</td>
                <td>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-primary btn-view-details" data-id="${transaction.id}">
                            <i class="mdi mdi-eye"></i> Review
                        </button>
                    </div>
                </td>
            `;

            transactionsTableBody.appendChild(row);
        });

        // Add event listeners to view details buttons
        document.querySelectorAll(".btn-view-details").forEach((button) => {
            button.addEventListener("click", function () {
                const transactionId = this.getAttribute("data-id");
                viewTransactionDetails(transactionId);
            });
        });

        // Add event listeners to print invoice buttons
        document.querySelectorAll(".btn-print-invoice").forEach((button) => {
            button.addEventListener("click", function () {
                const transactionId = this.getAttribute("data-id");
                showInvoicePreviewModal(transactionId);
            });
        });

        // Add event listeners to transaction checkboxes
        document.querySelectorAll(".transaction-checkbox").forEach((checkbox) => {
            checkbox.addEventListener("change", function () {
                const transactionData = JSON.parse(this.getAttribute("data-transaction"));

                if (this.checked) {
                    // Add to selected transactions if not already there
                    if (!selectedTransactions.some(t => t.id === transactionData.id)) {
                        selectedTransactions.push(transactionData);
                    }
                } else {
                    // Remove from selected transactions
                    selectedTransactions = selectedTransactions.filter(t => t.id !== transactionData.id);
                }

                // Update the Generate Invoice button state
                updateGenerateInvoiceButton();
            });
        });

        // Update the select all checkbox state
        updateSelectAllCheckbox();
    }

    // Function to render pagination
    function renderPagination(data) {
        paginationContainer.innerHTML = "";

        if (data.last_page <= 1) {
            return;
        }

        const ul = document.createElement("ul");
        ul.className = "pagination pagination-rounded justify-content-end mb-0";

        // Previous page link
        const prevLi = document.createElement("li");
        prevLi.className = `page-item ${
            data.current_page === 1 ? "disabled" : ""
        }`;
        prevLi.innerHTML = `<a class="page-link" href="javascript:void(0);" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a>`;
        if (data.current_page > 1) {
            prevLi
                .querySelector("a")
                .addEventListener("click", () =>
                    loadTransactions(data.current_page - 1, currentSortField, currentSortOrder)
                );
        }
        ul.appendChild(prevLi);

        // Page links
        const startPage = Math.max(1, data.current_page - 2);
        const endPage = Math.min(data.last_page, data.current_page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const li = document.createElement("li");
            li.className = `page-item ${
                i === data.current_page ? "active" : ""
            }`;
            li.innerHTML = `<a class="page-link" href="javascript:void(0);">${i}</a>`;
            if (i !== data.current_page) {
                li.querySelector("a").addEventListener("click", () =>
                    loadTransactions(i, currentSortField, currentSortOrder)
                );
            }
            ul.appendChild(li);
        }

        // Next page link
        const nextLi = document.createElement("li");
        nextLi.className = `page-item ${
            data.current_page === data.last_page ? "disabled" : ""
        }`;
        nextLi.innerHTML = `<a class="page-link" href="javascript:void(0);" aria-label="Next"><span aria-hidden="true">&raquo;</span></a>`;
        if (data.current_page < data.last_page) {
            nextLi
                .querySelector("a")
                .addEventListener("click", () =>
                    loadTransactions(data.current_page + 1, currentSortField, currentSortOrder)
                );
        }
        ul.appendChild(nextLi);

        paginationContainer.appendChild(ul);
    }

    // Function to update entries info
    function updateEntriesInfo(data) {
        if (data.total === 0) {
            entriesInfo.textContent = "Showing 0 to 0 of 0 entries";
            return;
        }

        const start = (data.current_page - 1) * data.per_page + 1;
        const end = Math.min(data.current_page * data.per_page, data.total);
        entriesInfo.textContent = `Showing ${start} to ${end} of ${data.total} entries`;
    }

    // Function to update the Generate Invoice button state
    function updateGenerateInvoiceButton() {
        if (generateInvoiceBtn) {
            generateInvoiceBtn.disabled = selectedTransactions.length === 0;
        }
    }

    // Function to update the Select All checkbox state
    function updateSelectAllCheckbox() {
        if (selectAllTransactionsCheckbox) {
            const checkboxes = document.querySelectorAll('.transaction-checkbox:not([disabled])');
            const checkedCheckboxes = document.querySelectorAll('.transaction-checkbox:checked:not([disabled])');

            if (checkboxes.length === 0) {
                selectAllTransactionsCheckbox.checked = false;
                selectAllTransactionsCheckbox.indeterminate = false;
            } else if (checkedCheckboxes.length === 0) {
                selectAllTransactionsCheckbox.checked = false;
                selectAllTransactionsCheckbox.indeterminate = false;
            } else if (checkedCheckboxes.length === checkboxes.length) {
                selectAllTransactionsCheckbox.checked = true;
                selectAllTransactionsCheckbox.indeterminate = false;
            } else {
                selectAllTransactionsCheckbox.checked = false;
                selectAllTransactionsCheckbox.indeterminate = true;
            }
        }
    }

    // Function to view transaction details
    function viewTransactionDetails(transactionId) {
        fetch(`/sales/transactions/${transactionId}`)
            .then((response) => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then((transaction) => {
                // Fill transaction details
                document.getElementById("detail-site").textContent =
                    transaction.site
                        ? transaction.site.site_name
                        : transaction.site_id;
                document.getElementById("detail-unit-code").textContent =
                    transaction.unit ? transaction.unit.unit_code : "-";
                document.getElementById("detail-unit-type").textContent =
                    transaction.unit ? transaction.unit.unit_type : "-";
                document.getElementById("detail-invoice-number").textContent =
                    transaction.invoice ? transaction.invoice.no_invoice : "-";
                document.getElementById("detail-po-number").textContent =
                    transaction.po_number || "-";

                // Update DO/SPB number label based on site
                const doNumberLabel = document.getElementById("detail-do-number-label");
                if (doNumberLabel) {
                    doNumberLabel.textContent = transaction.site && transaction.site.site_id === 'IMK' ? 'No SPB' : 'Nomor DO';
                }

                // Update DO/SPB number value based on site
                const doNumberValue = document.getElementById("detail-do-number");
                if (doNumberValue) {
                    doNumberValue.textContent = transaction.site && transaction.site.site_id === 'IMK'
                        ? (transaction.noSPB || "-")
                        : (transaction.do_number || "-");
                }

                // Format and display updated_at as Tanggal Dibuat
                const createdAt = new Date(transaction.updated_at);
                document.getElementById("detail-created-at").textContent =
                    formatDate(createdAt);

                // Display sales notes
                document.getElementById("detail-sales-notes").textContent =
                    transaction.sales_notes || "-";

                // Fill parts table
                const partsTableBody = document.getElementById(
                    "detail-parts-table-body"
                );
                partsTableBody.innerHTML = "";

                let totalPrice = 0;

                if (transaction.parts && transaction.parts.length > 0) {
                    transaction.parts.forEach((part) => {
                        const partPrice = parseFloat(part.price) || 0;
                        const partQuantity = parseInt(part.quantity) || 0;
                        const partTotal = partPrice * partQuantity;
                        totalPrice += partTotal;

                        const row = document.createElement("tr");
                        row.innerHTML = `
                            <td>${
                                part.part_inventory?.part?.part_code || "-"
                            }</td>
                            <td>${
                                part.part_inventory?.part?.part_name || "-"
                            }</td>
                            <td>${part.quantity}</td>
                            <td>${formatCurrency(partPrice)}</td>
                            <td>${formatCurrency(partTotal)}</td>
                        `;
                        partsTableBody.appendChild(row);
                    });
                } else {
                    const emptyRow = document.createElement("tr");
                    emptyRow.innerHTML =
                        '<td colspan="5" class="text-center">Tidak ada data part</td>';
                    partsTableBody.appendChild(emptyRow);
                }

                // Set total price
                document.getElementById("detail-total-price").textContent =
                    formatCurrency(totalPrice);

                // Handle attachment display
                const attachmentPreview =
                    document.getElementById("attachment-preview");
                const attachmentActions =
                    document.getElementById("attachment-actions");
                const downloadAttachment = document.getElementById(
                    "download-attachment"
                );

                // Reset attachment section
                attachmentPreview.innerHTML = "";
                attachmentActions.classList.add("d-none");

                if (transaction.attachment_path) {
                    // Set download link
                    const attachmentUrl = `/assets/lampiranunits/${transaction.attachment_path}`;
                    downloadAttachment.href = attachmentUrl;
                    attachmentActions.classList.remove("d-none");

                    // Determine file type and display preview accordingly
                    const fileExtension = transaction.attachment_path
                        .split(".")
                        .pop()
                        .toLowerCase();

                    if (["jpg", "jpeg", "png", "gif"].includes(fileExtension)) {
                        // Image preview
                        attachmentPreview.innerHTML = `
                            <img src="${attachmentUrl}" style="width: 100%; height: 100%; object-fit: contain;" alt="Attachment Preview">
                        `;
                    } else if (fileExtension === "pdf") {
                        // PDF preview
                        attachmentPreview.innerHTML = `
                            <iframe src="${attachmentUrl}" style="width: 100%; height: 100%; border: none;" allowfullscreen></iframe>
                        `;
                    } else {
                        // Other file types - show icon
                        attachmentPreview.innerHTML = `
                            <div class="d-flex flex-column align-items-center justify-content-center h-100">
                                <i class="mdi mdi-file-document-outline" style="font-size: 84px;"></i>
                                <p>${transaction.attachment_path}</p>
                            </div>
                        `;
                    }
                } else {
                    // No attachment - just leave the preview area empty
                    attachmentPreview.innerHTML = "";
                }

                // Set form values for status update
                transactionIdInput.value = transaction.id;
                salesNotesTextarea.value = transaction.sales_notes || "";

                // Set form values for notes update
                document.getElementById('notes-transaction-id').value = transaction.id;
                document.getElementById('notes_sales_notes').value = transaction.sales_notes || "";

                // Get the status update container and toggle button
                const statusUpdateContainer = document.getElementById('status-update-container');
                const notesUpdateContainer = document.getElementById('notes-update-container');
                const toggleBtn = document.getElementById("btn-toggle-status-form");
                const toggleNotesBtn = document.getElementById("btn-toggle-notes-form");

                // Initially hide the status update form and notes update form
                statusUpdateContainer.style.display = 'none';
                notesUpdateContainer.style.display = 'none';

                // Check if transaction is completed before setting up the toggle buttons
                if (transaction.status === 'Selesai') {
                    // Hide the status toggle button completely for completed transactions
                    toggleBtn.style.display = 'none';
                } else {
                    // Show and set up the status toggle button for non-completed transactions
                    toggleBtn.style.display = 'inline-block';
                    toggleBtn.innerHTML = '<i class="mdi mdi-pencil"></i> Ubah Status';
                    toggleBtn.classList.remove('btn-danger');
                    toggleBtn.classList.add('btn-light');
                }

                // Always show the notes toggle button
                toggleNotesBtn.style.display = 'inline-block';
                toggleNotesBtn.innerHTML = '<i class="mdi mdi-note-edit"></i> Catatan Sales';
                toggleNotesBtn.classList.remove('btn-danger');
                toggleNotesBtn.classList.add('btn-info');

                // Remove overlays if they exist
                const existingStatusOverlay = document.getElementById('status-form-overlay');
                if (existingStatusOverlay) {
                    document.body.removeChild(existingStatusOverlay);
                }

                const existingNotesOverlay = document.getElementById('notes-form-overlay');
                if (existingNotesOverlay) {
                    document.body.removeChild(existingNotesOverlay);
                }

                // Disable form if status is already 'Selesai'
                const updateStatusButton = updateStatusForm.querySelector("button[type='submit']");

                if (transaction.status === 'Selesai') {
                    // Disable form elements
                    statusSelect.disabled = true;
                    salesNotesTextarea.disabled = true;
                    updateStatusButton.disabled = true;

                    // Add a note that the transaction is already completed
                    const formNote = document.createElement("div");
                    formNote.className = "alert alert-info mt-3";
                    formNote.innerHTML = "<i class='mdi mdi-information-outline'></i> Transaksi ini sudah selesai dan tidak dapat diubah lagi.";

                    // Remove any existing notes first
                    const existingNote = updateStatusForm.querySelector(".alert");
                    if (existingNote) {
                        existingNote.remove();
                    }

                    updateStatusForm.appendChild(formNote);
                } else {
                    // Enable form elements
                    statusSelect.disabled = false;
                    salesNotesTextarea.disabled = false;
                    updateStatusButton.disabled = false;

                    // Remove any existing form notes
                    const existingNote = updateStatusForm.querySelector(".alert");
                    if (existingNote) {
                        existingNote.remove();
                    }
                }

                // Show modal
                $('#transaction-details-modal').modal('show');
            })
            .catch((error) => {
                Swal.fire({
                    title: "Error!",
                    text: "Gagal memuat detail transaksi. Silakan coba lagi.",
                    icon: "error",
                    confirmButtonText: "OK",
                });
            });
    }

    // Function to view invoiced transaction details (without edit button)
    function viewInvoicedTransactionDetails(transactionId) {
        fetch(`/sales/transactions/${transactionId}`)
            .then((response) => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then((transaction) => {
                // Fill transaction details
                document.getElementById("detail-site").textContent =
                    transaction.site
                        ? transaction.site.site_name
                        : transaction.site_id;
                document.getElementById("detail-unit-code").textContent =
                    transaction.unit ? transaction.unit.unit_code : "-";
                document.getElementById("detail-unit-type").textContent =
                    transaction.unit ? transaction.unit.unit_type : "-";
                document.getElementById("detail-invoice-number").textContent =
                    transaction.invoice ? transaction.invoice.no_invoice : "-";
                document.getElementById("detail-po-number").textContent =
                    transaction.po_number || "-";

                // Update DO/SPB number label based on site
                const doNumberLabel = document.getElementById("detail-do-number-label");
                if (doNumberLabel) {
                    doNumberLabel.textContent = transaction.site && transaction.site.site_id === 'IMK' ? 'No SPB' : 'Nomor DO';
                }

                // Update DO/SPB number value based on site
                const doNumberValue = document.getElementById("detail-do-number");
                if (doNumberValue) {
                    doNumberValue.textContent = transaction.site && transaction.site.site_id === 'IMK'
                        ? (transaction.noSPB || "-")
                        : (transaction.do_number || "-");
                }

                // Format and display updated_at as Tanggal Dibuat
                const createdAt = new Date(transaction.updated_at);
                document.getElementById("detail-created-at").textContent =
                    formatDate(createdAt);

                // Display sales notes
                document.getElementById("detail-sales-notes").textContent =
                    transaction.sales_notes || "-";

                // Fill parts table
                const partsTableBody = document.getElementById(
                    "detail-parts-table-body"
                );
                partsTableBody.innerHTML = "";

                let totalPrice = 0;

                if (transaction.parts && transaction.parts.length > 0) {
                    transaction.parts.forEach((part) => {
                        const partPrice = parseFloat(part.price) || 0;
                        const partQuantity = parseInt(part.quantity) || 0;
                        const partTotal = partPrice * partQuantity;
                        totalPrice += partTotal;

                        const row = document.createElement("tr");
                        row.innerHTML = `
                            <td>${
                                part.part_inventory?.part?.part_code || "-"
                            }</td>
                            <td>${
                                part.part_inventory?.part?.part_name || "-"
                            }</td>
                            <td>${part.quantity}</td>
                            <td>${formatCurrency(partPrice)}</td>
                            <td>${formatCurrency(partTotal)}</td>
                        `;
                        partsTableBody.appendChild(row);
                    });
                } else {
                    const emptyRow = document.createElement("tr");
                    emptyRow.innerHTML =
                        '<td colspan="5" class="text-center">Tidak ada data part</td>';
                    partsTableBody.appendChild(emptyRow);
                }

                // Set total price
                document.getElementById("detail-total-price").textContent =
                    formatCurrency(totalPrice);

                // Handle attachment display
                const attachmentPreview =
                    document.getElementById("attachment-preview");
                const attachmentActions =
                    document.getElementById("attachment-actions");
                const downloadAttachment = document.getElementById(
                    "download-attachment"
                );

                // Reset attachment section
                attachmentPreview.innerHTML = "";
                attachmentActions.classList.add("d-none");

                if (transaction.attachment_path) {
                    // Set download link
                    const attachmentUrl = `/assets/lampiranunits/${transaction.attachment_path}`;
                    downloadAttachment.href = attachmentUrl;
                    attachmentActions.classList.remove("d-none");

                    // Determine file type and display preview accordingly
                    const fileExtension = transaction.attachment_path
                        .split(".")
                        .pop()
                        .toLowerCase();

                    if (["jpg", "jpeg", "png", "gif"].includes(fileExtension)) {
                        // Image preview
                        attachmentPreview.innerHTML = `
                            <img src="${attachmentUrl}" style="width: 100%; height: 100%; object-fit: contain;" alt="Attachment Preview">
                        `;
                    } else if (fileExtension === "pdf") {
                        // PDF preview
                        attachmentPreview.innerHTML = `
                            <iframe src="${attachmentUrl}" style="width: 100%; height: 100%; border: none;" allowfullscreen></iframe>
                        `;
                    } else {
                        // Other file types - show icon
                        attachmentPreview.innerHTML = `
                            <div class="d-flex flex-column align-items-center justify-content-center h-100">
                                <i class="mdi mdi-file-document-outline" style="font-size: 84px;"></i>
                                <p>${transaction.attachment_path}</p>
                            </div>
                        `;
                    }
                } else {
                    // No attachment - just leave the preview area empty
                    attachmentPreview.innerHTML = "";
                }

                // Hide the status update and notes buttons for invoiced transactions
                const toggleStatusBtn = document.getElementById("btn-toggle-status-form");
                const toggleNotesBtn = document.getElementById("btn-toggle-notes-form");
                if (toggleStatusBtn) {
                    toggleStatusBtn.style.display = 'none';
                }
                if (toggleNotesBtn) {
                    toggleNotesBtn.style.display = 'none';
                }

                // Show modal
                $('#transaction-details-modal').modal('show');
            })
            .catch((error) => {
                Swal.fire({
                    title: "Error!",
                    text: "Gagal memuat detail transaksi. Silakan coba lagi.",
                    icon: "error",
                    confirmButtonText: "OK",
                });
            });
    }

    // Function to update transaction status
    function updateTransactionStatus() {
        const transactionId = transactionIdInput.value;
        const status = statusSelect.value;
        const salesNotes = salesNotesTextarea.value;

        // Validate input
        // Remarks are now optional - no validation required

        // Show confirmation dialog with special warning for 'Selesai' status
        const confirmationText = status === 'Selesai'
            ? `Apakah Anda yakin ingin mengubah status transaksi ini menjadi "Selesai"? Setelah disimpan, Anda tidak dapat mengedit data ini lagi.`
            : `Apakah Anda yakin ingin mengubah status transaksi menjadi "${status}"?`;

        Swal.fire({
            title: "Konfirmasi",
            text: confirmationText,
            icon: "question",
            showCancelButton: true,
            confirmButtonText: "Ya, Ubah Status",
            cancelButtonText: "Batal",
        }).then((result) => {
            if (result.isConfirmed) {
                // Proceed with status update
                fetch(`/sales/transactions/${transactionId}/status`, {
                    method: "PUT",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document
                            .querySelector('meta[name="csrf-token"]')
                            .getAttribute("content"),
                    },
                    body: JSON.stringify({
                        status: status,
                        sales_notes: salesNotes,
                    }),
                })
                    .then((response) => {
                        if (!response.ok) {
                            throw new Error(
                                `HTTP error! status: ${response.status}`
                            );
                        }
                        return response.json();
                    })
                    .then((data) => {
                        if (data.success) {
                            Swal.fire({
                                title: "Sukses!",
                                text: "Status transaksi berhasil diperbarui.",
                                icon: "success",
                                confirmButtonText: "OK",
                            }).then(() => {
                                // Hide status form and remove overlay
                                const statusUpdateContainer = document.getElementById('status-update-container');
                                statusUpdateContainer.style.display = 'none';

                                // Reset toggle button
                                const toggleBtn = document.getElementById("btn-toggle-status-form");
                                if (toggleBtn) {
                                    // If status was set to 'Selesai', hide the button
                                    if (status === 'Selesai') {
                                        toggleBtn.style.display = 'none';
                                    } else {
                                        toggleBtn.innerHTML = '<i class="mdi mdi-pencil"></i> Ubah Status';
                                        toggleBtn.classList.remove('btn-danger');
                                        toggleBtn.classList.add('btn-light');
                                    }
                                }

                                // Remove overlay
                                const overlay = document.getElementById('status-form-overlay');
                                if (overlay) {
                                    document.body.removeChild(overlay);
                                }

                                // Close modal
                                $('#transaction-details-modal').modal('hide');

                                // Reload transactions
                                loadTransactions();
                            });
                        } else {
                            throw new Error(
                                data.message ||
                                    "Failed to update transaction status"
                            );
                        }
                    })
                    .catch((error) => {
                        Swal.fire({
                            title: "Error!",
                            text: "Gagal memperbarui status transaksi. Silakan coba lagi.",
                            icon: "error",
                            confirmButtonText: "OK",
                        });
                    });
            }
        });
    }

    // Helper function to format date
    function formatDate(date) {
        const months = [
            "Januari",
            "Februari",
            "Maret",
            "April",
            "Mei",
            "Juni",
            "Juli",
            "Agustus",
            "September",
            "Oktober",
            "November",
            "Desember",
        ];

        const day = date.getDate();
        const month = months[date.getMonth()];
        const year = date.getFullYear();

        return `${day} ${month} ${year}`;
    }

    // Helper function to format currency
    function formatCurrency(amount) {
        return "Rp " + amount.toLocaleString("id-ID");
    }

    // Helper function to format invoice number with default format
    function formatInvoiceNumber(number, month, year) {
        // Ensure the number is padded to 3 digits
        const paddedNumber = String(number).padStart(3, '0');
        // Ensure month is padded to 2 digits
        const paddedMonth = String(month).padStart(2, '0');
        // Format the invoice number with default format
        // Users can change the format after the first part
        return `${paddedNumber}/INV-PWB/${paddedMonth}/${year}`;
    }

    // Helper function to extract and increment invoice number
    function getNextInvoiceNumber(invoiceNumber) {
        // Extract parts from the invoice number
        const parts = invoiceNumber.split('/');
        if (parts.length < 2) return null; // Need at least one slash

        // Extract the numeric part and increment it
        const numericPart = parseInt(parts[0]);
        if (isNaN(numericPart)) return null; // First part must be a number

        const nextNumericPart = numericPart + 1;

        // Pad the numeric part to the same length as the original
        const originalLength = parts[0].length;
        const paddedNumericPart = String(nextNumericPart).padStart(originalLength, '0');

        // Keep the rest of the format exactly as it was
        const restOfFormat = invoiceNumber.substring(invoiceNumber.indexOf('/'));

        // Return the new invoice number
        return paddedNumericPart + restOfFormat;
    }

    // Helper function to get status badge
    function getStatusBadge(status) {
        let badgeClass = "";

        switch (status) {
            case "On Process":
                badgeClass = "badge bg-warning";
                break;
            case "MR":
                badgeClass = "badge bg-info";
                break;
            case "Pending":
                badgeClass = "badge bg-secondary";
                break;
            case "Ready WO":
                badgeClass = "badge bg-success";
                break;
            case "Ready PO":
                badgeClass = "badge bg-primary";
                break;
            case "Perbaikan":
                badgeClass = "badge bg-danger";
                break;
            case "Selesai":
                badgeClass = "badge bg-success";
                break;
            default:
                badgeClass = "badge bg-secondary";
        }

        return `<span class="${badgeClass}">${status}</span>`;
    }

    // Debounce function for search input
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Function to close the status update form
    function closeStatusUpdateForm() {
        const statusUpdateContainer = document.getElementById('status-update-container');
        statusUpdateContainer.style.display = 'none';

        // Reset toggle button
        const toggleBtn = document.getElementById("btn-toggle-status-form");
        if (toggleBtn) {
            toggleBtn.innerHTML = '<i class="mdi mdi-pencil"></i> Ubah Status';
            toggleBtn.classList.remove('btn-danger');
            toggleBtn.classList.add('btn-light');
        }

        // Remove overlay
        const overlay = document.getElementById('status-form-overlay');
        if (overlay) {
            document.body.removeChild(overlay);
        }
    }

    // Function to show invoice preview modal for a single transaction or invoice
    function showInvoicePreviewModal(id) {
        // Check if this is an invoice ID or a transaction ID
        if (id.toString().startsWith('inv-')) {
            // This is an invoice ID, open the preview directly
            window.open(`/sales/invoices/${id}/preview`, '_blank');
            return;
        }

        // This is a transaction ID, check if it has an invoice
        fetch(`/sales/transactions/${id}/invoice`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.exists && data.invoice) {
                    // Transaction has an invoice, open the preview
                    window.open(`/sales/transactions/${id}/preview-invoice`, '_blank');
                } else {
                    // Transaction doesn't have an invoice, show a message
                    Swal.fire({
                        title: "Informasi",
                        text: "Transaksi ini belum memiliki invoice",
                        icon: "info",
                        confirmButtonText: "OK"
                    });
                }
            })
            .catch(error => {
                Swal.fire({
                    title: "Error!",
                    text: "Gagal memuat data invoice. Silakan coba lagi.",
                    icon: "error",
                    confirmButtonText: "OK",
                });
            });
    }

    // Function to check if selected transactions are already invoiced
    function checkTransactionsInvoiced() {
        const transactionIds = selectedTransactions.map(t => t.id);

        fetch('/sales/check-transactions-invoiced', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ transaction_ids: transactionIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.has_invoiced) {
                    // Some transactions are already invoiced
                    let message = 'Beberapa transaksi sudah memiliki invoice:<br><ul>';
                    data.invoiced_transactions.forEach(t => {
                        message += `<li>${t.unit_code} - Invoice: ${t.invoice_number}</li>`;
                    });
                    message += '</ul>';

                    Swal.fire({
                        title: "Peringatan",
                        html: message,
                        icon: "warning",
                        confirmButtonText: "OK"
                    });
                } else {
                    // All transactions are available for invoicing
                    showMultiInvoiceForm();
                }
            } else {
                Swal.fire({
                    title: "Error!",
                    text: data.message || "Terjadi kesalahan saat memeriksa status invoice",
                    icon: "error",
                    confirmButtonText: "OK"
                });
            }
        })
        .catch(error => {
            Swal.fire({
                title: "Error!",
                text: "Terjadi kesalahan saat memeriksa status invoice",
                icon: "error",
                confirmButtonText: "OK"
            });
        });
    }

    // Add event listener for file input validation
    document.getElementById('signed-document').addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            // Check file size (max 10MB)
            const maxSize = 10 * 1024 * 1024; // 10MB in bytes
            if (file.size > maxSize) {
                Swal.fire({
                    icon: 'error',
                    title: 'File terlalu besar',
                    text: 'Ukuran file maksimal adalah 10MB'
                });
                this.value = '';
                return;
            }

            // Check file extension
            const fileExtension = file.name.split('.').pop().toLowerCase();
            if (fileExtension !== 'pdf') {
                Swal.fire({
                    icon: 'error',
                    title: 'Format file tidak didukung',
                    text: 'Format file harus PDF'
                });
                this.value = '';
                return;
            }
        }
    });

    // Function to show multi-transaction invoice form
    function showMultiInvoiceForm() {
        // Clear the form for a new invoice
        document.getElementById('invoice-form').reset();

        // Hide document preview
        document.getElementById('signed-document-preview').classList.add('d-none');

        // Set today's date as default
        const today = new Date();
        const formattedDate = today.toISOString().split('T')[0];
        document.getElementById('invoice-tanggal').value = formattedDate;

        // Set due date (30 days from today) as default
        const dueDate = new Date(today);
        dueDate.setDate(dueDate.getDate() + 30);
        const formattedDueDate = dueDate.toISOString().split('T')[0];
        document.getElementById('invoice-due-date').value = formattedDueDate;

        // Set default PPN value
        document.getElementById('invoice-ppn').value = '0.11';

        // Set the transaction IDs in the form
        document.getElementById('invoice-transaction-ids').value = JSON.stringify(selectedTransactions.map(t => t.id));

        // Fetch the first selected transaction to get the DO number
        if (selectedTransactions.length > 0) {
            fetch(`/sales/transactions/${selectedTransactions[0].id}`)
                .then(response => response.json())
                .then(transaction => {
                    // Check if the site is IMK to use noSPB instead of do_number
                    let doNumber = "Part/AC";
                    if (transaction.site && transaction.site.site_id === 'IMK') {
                        doNumber = transaction.noSPB || "Part/AC";
                    } else {
                        doNumber = transaction.do_number || "Part/AC";
                    }
                    document.getElementById('invoice-do-number').value = doNumber;
                })
                .catch(error => {
                    // Set default DO number if fetch fails
                    document.getElementById('invoice-do-number').value = "Part/AC";
                });
        } else {
            // Set default DO number if no transactions selected
            document.getElementById('invoice-do-number').value = "Part/AC";
        }

        // Show the selected transactions in the container
        const selectedTransactionsContainer = document.getElementById('selected-transactions-container');
        const selectedTransactionsList = document.getElementById('selected-transactions-list');

        if (selectedTransactionsContainer && selectedTransactionsList) {
            selectedTransactionsList.innerHTML = '';

            selectedTransactions.forEach(transaction => {
                const transactionItem = document.createElement('div');
                transactionItem.className = 'mb-1';
                transactionItem.innerHTML = `
                    <span class="badge bg-primary">${transaction.site_name}</span>
                    <span class="ms-2">${transaction.unit_code} - ${transaction.unit_type}</span>
                `;
                selectedTransactionsList.appendChild(transactionItem);
            });

            selectedTransactionsContainer.classList.remove('d-none');
        }

        // Fetch the next invoice number
        fetch('/sales/latest-invoice')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.latest_invoice) {
                    // Use the exact format from the last invoice
                    const lastInvoiceNumber = data.latest_invoice.no_invoice;
                    const nextInvoiceNumber = getNextInvoiceNumber(lastInvoiceNumber);

                    if (nextInvoiceNumber) {
                        document.getElementById('invoice-no').value = nextInvoiceNumber;
                    } else {
                        // Fallback to default format
                        const today = new Date();
                        const month = today.getMonth() + 1;
                        const year = today.getFullYear();
                        document.getElementById('invoice-no').value = formatInvoiceNumber(1, month, year);
                    }
                } else {
                    // Fallback to default format
                    const today = new Date();
                    const month = today.getMonth() + 1;
                    const year = today.getFullYear();
                    document.getElementById('invoice-no').value = formatInvoiceNumber(1, month, year);
                }
            })
            .catch(error => {
                // Fallback to a simple format
                const today = new Date();
                const month = today.getMonth() + 1;
                const year = today.getFullYear();
                document.getElementById('invoice-no').value = formatInvoiceNumber(1, month, year);
            });

        // Show the modal
        $('#invoice-form-modal').modal('show');
    }

    // Function to save invoice data
    function saveInvoice() {
        // Get form elements
        const invoiceForm = document.getElementById('invoice-form');
        const signedDocumentInput = document.getElementById('signed-document');

        // Check if we're updating an existing invoice
        const invoiceId = document.getElementById('invoice-id') ? document.getElementById('invoice-id').value : null;
        const isUpdate = !!invoiceId;

        // Create FormData object for file upload
        const formData = new FormData();

        // Add all form fields to FormData
        // Parse transaction_ids as JSON and append each ID individually for proper array handling
        const transactionIdsValue = document.getElementById('invoice-transaction-ids').value || '[]';
        const transactionIds = JSON.parse(transactionIdsValue);
        transactionIds.forEach(id => {
            formData.append('transaction_ids[]', id);
        });
        formData.append('customer', document.getElementById('invoice-customer').value);
        formData.append('location', document.getElementById('invoice-location').value);
        formData.append('no_invoice', document.getElementById('invoice-no').value);
        formData.append('sn', document.getElementById('invoice-sn').value);
        formData.append('trouble', document.getElementById('invoice-do-number').value || "Part/AC"); // Use DO number, default to "Part/AC"
        formData.append('lokasi', document.getElementById('invoice-lokasi').value);
        formData.append('tanggal_invoice', document.getElementById('invoice-tanggal').value);
        formData.append('due_date', document.getElementById('invoice-due-date').value);
        formData.append('ppn', document.getElementById('invoice-ppn').value);
        formData.append('notes', document.getElementById('invoice-notes').value);

        // Add the file if selected
        if (signedDocumentInput && signedDocumentInput.files.length > 0) {
            formData.append('signed_document', signedDocumentInput.files[0]);
        }

        if (isUpdate) {
            formData.append('invoice_id', invoiceId);
            formData.append('_method', 'PUT'); // For Laravel method spoofing
        }

        // Only validate that the invoice number starts with digits before a slash
        const invoiceNumberPattern = /^\d+\//;
        const noInvoice = document.getElementById('invoice-no').value;
        if (!invoiceNumberPattern.test(noInvoice)) {
            Swal.fire({
                title: "Format Nomor Invoice Salah",
                text: "Nomor invoice harus dimulai dengan angka diikuti dengan tanda slash (/). Contoh: 001/...",
                icon: "error",
                confirmButtonText: "OK"
            });
            return;
        }

        // Validate due date is after invoice date
        const invoiceDate = new Date(document.getElementById('invoice-tanggal').value);
        const dueDate = new Date(document.getElementById('invoice-due-date').value);
        if (dueDate <= invoiceDate) {
            Swal.fire({
                title: "Tanggal Jatuh Tempo Tidak Valid",
                text: "Tanggal jatuh tempo harus setelah tanggal invoice",
                icon: "error",
                confirmButtonText: "OK"
            });
            return;
        }

        // Show loading indicator
        Swal.fire({
            title: isUpdate ? "Memperbarui..." : "Menyimpan...",
            text: isUpdate ? "Mohon tunggu sementara invoice sedang diperbarui." : "Mohon tunggu sementara invoice sedang dibuat.",
            allowOutsideClick: false,
            allowEscapeKey: false,
            allowEnterKey: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // Send data to server
        fetch(isUpdate ? `/sales/invoices/${invoiceId}` : '/sales/invoices', {
            method: 'POST', // Always use POST for FormData (method spoofing for PUT)
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: formData
        })
        .then(response => {
            // Always return the response JSON, even if it's an error
            return response.json().then(data => {
                // Add the status to the data object
                data.status = response.status;
                return data;
            });
        })
        .then(data => {
            // Hide the loading indicator
            Swal.close();

            // Check if the request was successful
            if (data.success) {
                // Hide the modal
                $('#invoice-form-modal').modal('hide');

                // Show success message
                Swal.fire({
                    title: "Sukses!",
                    text: isUpdate ? "Invoice berhasil diperbarui." : "Invoice berhasil dibuat dan status transaksi diubah menjadi Selesai.",
                    icon: "success",
                    confirmButtonText: "OK"
                }).then(() => {
                    // Reset form state
                    document.getElementById('invoice-form').reset();
                    if (document.getElementById('invoice-id')) {
                        document.getElementById('invoice-id').value = '';
                    }
                    document.getElementById('invoice-form-modal-label').textContent = 'Buat Invoice';
                    document.getElementById('save-invoice-btn').textContent = 'Simpan';

                    // Clear selected transactions if creating new invoice
                    if (!isUpdate) {
                        selectedTransactions = [];
                        updateGenerateInvoiceButton();
                    }

                    // Reload the transactions to update the tables
                    loadTransactions();
                    loadInvoicedTransactions();

                    // Only open the preview in a new tab for new invoices, not for updates
                    if (!isUpdate) {
                        setTimeout(() => {
                            try {
                                if (data.invoice && data.invoice.id) {
                                    window.open(`/sales/invoices/inv-${data.invoice.id}/preview`, '_blank');
                                } else {
                                    console.error('Invoice ID not found in response data');
                                    Swal.fire({
                                        title: "Perhatian",
                                        text: "Invoice berhasil dibuat tetapi tidak dapat membuka preview. Silakan coba melihat invoice dari daftar invoice.",
                                        icon: "warning",
                                        confirmButtonText: "OK"
                                    });
                                }
                            } catch (error) {
                                console.error('Error opening invoice preview:', error);
                                Swal.fire({
                                    title: "Perhatian",
                                    text: "Invoice berhasil dibuat tetapi terjadi kesalahan saat membuka preview. Silakan coba melihat invoice dari daftar invoice.",
                                    icon: "warning",
                                    confirmButtonText: "OK"
                                });
                            }
                        }, 1000); // Increased delay to 1 second to give more time for the transaction to complete
                    }
                });
            } else {
                // Show error message
                Swal.fire({
                    title: "Error!",
                    text: data.message || (isUpdate ? "Gagal memperbarui data invoice. Silakan coba lagi." : "Gagal menyimpan data invoice. Silakan coba lagi."),
                    icon: "error",
                    confirmButtonText: "OK"
                });
            }
        })
        .catch(error => {
            // Hide the loading indicator
            Swal.close();
            // Show error message
            Swal.fire({
                title: "Error!",
                text: isUpdate ? "Terjadi kesalahan saat memperbarui data invoice. Silakan coba lagi." : "Terjadi kesalahan saat menyimpan data invoice. Silakan coba lagi.",
                icon: "error",
                confirmButtonText: "OK"
            }).then(() => {
                // Reload the page after a short delay to refresh the data
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            });
        });
    }

    // This function has been replaced with direct preview opening

    // Function to load invoiced transactions with current filters
    function loadInvoicedTransactions(page = 1, sortField = "", sortOrder = "asc") {
        // Show loading indicator
        if (invoicedTransactionsTableBody) {
            invoicedTransactionsTableBody.innerHTML = '<tr><td colspan="13" class="text-center">Loading...</td></tr>';

            // Update global sort variables
            if (sortField) {
                currentSortField = sortField;
                currentSortOrder = sortOrder;
            }

            // Build query parameters
            const params = new URLSearchParams({
                page: page,
                date_from: dateFrom.value,
                date_to: dateTo.value,
                site_id: siteFilter.value,
                unit_id: unitFilter.value,
                status: statusFilter.value,
                search: searchInput.value,
                payment_status: invoiceStatusFilter ? invoiceStatusFilter.value : '',
                invoice_search: invoiceSearchInput ? invoiceSearchInput.value : ''
            });

            // Add sort parameters if available
            if (currentSortField) {
                params.append('sort_field', currentSortField);
                params.append('sort_order', currentSortOrder);
            }

            // Fetch invoiced transactions
            fetch(`/sales/invoiced-transactions?${params}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    renderInvoicedTransactions(data);
                    renderInvoicedPagination(data);
                    updateInvoicedEntriesInfo(data);
                })
                .catch(error => {
                    invoicedTransactionsTableBody.innerHTML = '<tr><td colspan="13" class="text-center text-danger">Error loading data. Please try again.</td></tr>';
                });
        }
    }

    // Function to render invoices in the table
    function renderInvoicedTransactions(data) {
        if (!invoicedTransactionsTableBody) return;

        invoicedTransactionsTableBody.innerHTML = "";

        // Check if data exists and has data property
        if (!data || !data.data) {
            const emptyRow = document.createElement("tr");
            emptyRow.innerHTML =
                '<td colspan="12" class="text-center">Tidak ada data invoice</td>';
            invoicedTransactionsTableBody.appendChild(emptyRow);
            return;
        }

        // Convert data.data to array if it's an object
        let invoicesArray = [];

        if (Array.isArray(data.data)) {
            invoicesArray = data.data;
        } else if (typeof data.data === 'object' && data.data !== null) {
            // Convert object to array
            invoicesArray = Object.values(data.data);
        }

        // Check if we have any invoices to display
        if (invoicesArray.length === 0) {
            const emptyRow = document.createElement("tr");
            emptyRow.innerHTML =
                '<td colspan="12" class="text-center">Tidak ada data invoice</td>';
            invoicedTransactionsTableBody.appendChild(emptyRow);
            return;
        }

        // Function to format currency as Rupiah
        function formatRupiah(amount) {
            if (amount === undefined || amount === null) return 'Rp 0';
            return 'Rp ' + new Intl.NumberFormat('id-ID').format(Math.round(amount));
        }

        // Now we can safely iterate through the invoices
        invoicesArray.forEach((invoice, index) => {
            // Skip invoices that have a document_path - they should only be shown in the invoice page
            if (invoice.document_path) {
                return;
            }

            const row = document.createElement("tr");

            // Format invoice date
            const invoiceDate = invoice.tanggal_invoice
                ? new Date(invoice.tanggal_invoice)
                : null;
            const formattedInvoiceDate = invoiceDate ? formatDate(invoiceDate) : "-";

            // Format unit list
            const unitList = invoice.unit_list || '-';

            // Format monetary values
            const formattedSubtotal = formatRupiah(invoice.subtotal);
            const formattedTax = formatRupiah(invoice.tax_amount);
            const formattedTotal = formatRupiah(invoice.total_amount);

            // Determine invoice status class
            let statusClass = 'bg-secondary';
            if (invoice.invoice_status === 'Lunas') {
                statusClass = 'bg-success';
            } else if (invoice.invoice_status === 'Jatuh Tempo') {
                statusClass = 'bg-danger';
            } else if (invoice.invoice_status === 'Belum Lunas') {
                statusClass = 'bg-warning';
            }

            row.innerHTML = `
                <td>${(data.current_page - 1) * data.per_page + index + 1}</td>
                <td>${invoice.no_invoice || "-"}</td>
                <td>${unitList}</td>
                <td>${formattedInvoiceDate}</td>
                <td>${invoice.customer || "-"}</td>
                <td>${invoice.sn || "-"}</td>
                <td>${formattedSubtotal}</td>
                <td>${formattedTax}</td>
                <td>${formattedTotal}</td>
                <td>
                    <span class="badge ${statusClass}">${invoice.invoice_status}</span>
                </td>
                <td>
                    <button class="btn btn-sm btn-${invoice.payment_status === 'Lunas' ? 'success' : 'warning'} btn-payment-status" data-id="${invoice.id}" data-status="${invoice.payment_status}">
                        ${invoice.payment_status}
                    </button>
                </td>
                <td>
                    ${invoice.document_path && !invoice.signed_document_path ?
                        `<a href="/assets/invoice_documents/${invoice.document_path}" target="_blank" class="btn btn-sm btn-info">
                            <i class="mdi mdi-file"></i> Lihat
                        </a>` :
                        '<span class="badge bg-secondary">Tidak ada</span>'
                    }
                </td>
                <td>
                    <button class="btn btn-sm btn-success btn-print-invoice" data-id="inv-${invoice.id}">
                        <i class="mdi mdi-printer"></i> Invoice
                    </button>
                </td>
                <td>
                    <button class="btn btn-sm btn-info btn-download-attachments" data-id="${invoice.id}">
                        <i class="mdi mdi-download"></i> Lampiran
                    </button>
                </td>
                <td>
                    <div class="btn-group">
                        ${invoice.payment_status !== 'Lunas' ?
                            `<button class="btn btn-sm btn-primary btn-edit-invoice" data-id="${invoice.id}">
                                <i class="mdi mdi-pencil"></i> Edit
                            </button>` : ''
                        }
                        ${invoice.payment_status !== 'Lunas' ?
                            `<button class="btn btn-sm btn-danger btn-delete-invoice" data-id="${invoice.id}">
                                <i class="mdi mdi-delete"></i> Hapus
                            </button>` : ''
                        }
                    </div>
                </td>
            `;

            invoicedTransactionsTableBody.appendChild(row);
        });

        // Add event listeners to print invoice buttons
        invoicedTransactionsTableBody.querySelectorAll(".btn-print-invoice").forEach((button) => {
            button.addEventListener("click", function () {
                const invoiceId = this.getAttribute("data-id");
                showInvoicePreviewModal(invoiceId);
            });
        });

        // Add event listeners to edit invoice buttons
        invoicedTransactionsTableBody.querySelectorAll(".btn-edit-invoice").forEach((button) => {
            button.addEventListener("click", function () {
                const invoiceId = this.getAttribute("data-id");
                editInvoice(invoiceId);
            });
        });

        // Add event listeners to payment status buttons
        invoicedTransactionsTableBody.querySelectorAll(".btn-payment-status").forEach((button) => {
            button.addEventListener("click", function () {
                const invoiceId = this.getAttribute("data-id");
                const currentStatus = this.getAttribute("data-status");
                showPaymentStatusModal(invoiceId, currentStatus);
            });
        });

        // Add event listeners to delete invoice buttons
        invoicedTransactionsTableBody.querySelectorAll(".btn-delete-invoice").forEach((button) => {
            button.addEventListener("click", function () {
                const invoiceId = this.getAttribute("data-id");
                deleteInvoice(invoiceId);
            });
        });

        // Add event listeners to download attachments buttons
        invoicedTransactionsTableBody.querySelectorAll(".btn-download-attachments").forEach((button) => {
            button.addEventListener("click", function () {
                const invoiceId = this.getAttribute("data-id");
                downloadInvoiceAttachments(invoiceId);
            });
        });
    }

    // Function to delete an invoice
    function deleteInvoice(invoiceId) {
        // Show confirmation dialog
        Swal.fire({
            title: "Konfirmasi Hapus",
            text: "Apakah Anda yakin ingin menghapus invoice ini? Tindakan ini akan mengembalikan status unit transaction menjadi 'Ready PO'.",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#d33",
            cancelButtonColor: "#3085d6",
            confirmButtonText: "Ya, Hapus!",
            cancelButtonText: "Batal"
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading indicator
                Swal.fire({
                    title: "Menghapus...",
                    text: "Mohon tunggu sementara invoice sedang dihapus.",
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Send delete request
                fetch(`/sales/invoices/${invoiceId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    Swal.close();

                    if (data.success) {
                        Swal.fire({
                            title: "Sukses!",
                            text: "Invoice berhasil dihapus dan status unit transaction dikembalikan menjadi 'Ready PO'.",
                            icon: "success",
                            confirmButtonText: "OK"
                        }).then(() => {
                            // Reload the tables
                            loadTransactions();
                            loadInvoicedTransactions();
                        });
                    } else {
                        Swal.fire({
                            title: "Error!",
                            text: data.message || "Gagal menghapus invoice. Silakan coba lagi.",
                            icon: "error",
                            confirmButtonText: "OK"
                        });
                    }
                })
                .catch(error => {
                    Swal.close();
                    Swal.fire({
                        title: "Error!",
                        text: "Terjadi kesalahan saat menghapus invoice. Silakan coba lagi.",
                        icon: "error",
                        confirmButtonText: "OK"
                    });
                });
            }
        });
    }

    // Function to edit an invoice
    function editInvoice(invoiceId) {
        // Show loading indicator
        Swal.fire({
            title: 'Loading...',
            text: 'Memuat data invoice',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // Fetch the invoice details
        fetch(`/sales/invoices/${invoiceId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Log the response for debugging
                console.log('Invoice data received:', data);

                // Check if the response has the expected structure
                if (!data || !data.success) {
                    throw new Error('Invalid response format');
                }

                // Get the invoice from the response
                const invoice = data.invoice;

                if (!invoice) {
                    throw new Error('Invoice data not found in response');
                }

                // Close loading indicator
                Swal.close();

                // Get the form
                const invoiceForm = document.getElementById('invoice-form');

                // Reset the form
                invoiceForm.reset();

                // Set the invoice ID for update
                let invoiceIdField = document.getElementById('invoice-id');
                if (!invoiceIdField) {
                    const hiddenField = document.createElement('input');
                    hiddenField.type = 'hidden';
                    hiddenField.id = 'invoice-id';
                    hiddenField.name = 'invoice_id';
                    invoiceForm.appendChild(hiddenField);
                    invoiceIdField = hiddenField;
                }
                invoiceIdField.value = invoiceId;

                // Set the form title
                document.getElementById('invoice-form-modal-label').textContent = 'Edit Invoice';

                // Helper function to format date for input without timezone issues
                function formatDateForInput(dateString) {
                    if (!dateString) return '';

                    // If it's already in YYYY-MM-DD format, return as is
                    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
                        return dateString;
                    }

                    // Handle different date formats from server
                    let cleanDateString = dateString;

                    if (dateString.includes('T')) {
                        // ISO format: YYYY-MM-DDTHH:MM:SS.000000Z
                        cleanDateString = dateString.split('T')[0];
                    } else if (dateString.includes(' ')) {
                        // MySQL format: YYYY-MM-DD HH:MM:SS
                        cleanDateString = dateString.split(' ')[0];
                    }

                    return cleanDateString;
                }

                try {
                    // Populate the form with invoice data
                    if (document.getElementById('invoice-customer')) {
                        document.getElementById('invoice-customer').value = invoice.customer || '';
                    }
                    if (document.getElementById('invoice-location')) {
                        document.getElementById('invoice-location').value = invoice.location || '';
                    }
                    if (document.getElementById('invoice-no')) {
                        document.getElementById('invoice-no').value = invoice.no_invoice || '';
                    }
                    if (document.getElementById('invoice-sn')) {
                        document.getElementById('invoice-sn').value = invoice.sn || '';
                    }
                    if (document.getElementById('invoice-do-number')) {
                        // Check if any of the unit transactions are from IMK site
                        let isIMKSite = false;
                        if (invoice.unit_transactions && Array.isArray(invoice.unit_transactions)) {
                            isIMKSite = invoice.unit_transactions.some(transaction =>
                                transaction.site && transaction.site.site_id === 'IMK'
                            );
                        }

                        // If IMK site, we should have already stored the noSPB value in the trouble field
                        document.getElementById('invoice-do-number').value = invoice.trouble || 'Part/AC';
                    }
                    if (document.getElementById('invoice-lokasi')) {
                        document.getElementById('invoice-lokasi').value = invoice.lokasi || '';
                    }
                    if (document.getElementById('invoice-tanggal')) {
                        document.getElementById('invoice-tanggal').value = formatDateForInput(invoice.tanggal_invoice);
                    }
                    if (document.getElementById('invoice-ppn')) {
                        document.getElementById('invoice-ppn').value = invoice.ppn || '0.11';
                    }
                    if (document.getElementById('invoice-notes')) {
                        document.getElementById('invoice-notes').value = invoice.notes || '';
                    }

                    // Show current document if exists
                    const signedDocumentPreview = document.getElementById('signed-document-preview');
                    const currentDocumentName = document.getElementById('current-document-name');
                    if (signedDocumentPreview && currentDocumentName) {
                        if (invoice.document_path) {
                            signedDocumentPreview.classList.remove('d-none');
                            currentDocumentName.textContent = invoice.document_path;

                            // Reset the preview container first to avoid duplicate links
                            const previewContainer = currentDocumentName.parentElement;
                            if (previewContainer) {
                                previewContainer.innerHTML = `<p>Dokumen saat ini: <span id="current-document-name">${invoice.document_path}</span>
                                    <a href="/assets/invoice_documents/${invoice.document_path}" target="_blank" class="btn btn-sm btn-info ms-2">
                                        <i class="mdi mdi-file"></i> Lihat
                                    </a>
                                </p>`;
                            }
                        } else {
                            signedDocumentPreview.classList.add('d-none');
                            currentDocumentName.textContent = '';
                        }
                    }

                    // Set due date
                    if (invoice.due_date && document.getElementById('invoice-due-date')) {
                        document.getElementById('invoice-due-date').value = formatDateForInput(invoice.due_date);
                    }

                    // Handle unit transactions if they exist
                    if (invoice.unit_transactions && Array.isArray(invoice.unit_transactions)) {
                        const transactionIds = invoice.unit_transactions.map(t => t.id);
                        if (document.getElementById('invoice-transaction-ids')) {
                            document.getElementById('invoice-transaction-ids').value = JSON.stringify(transactionIds);
                        }

                        // Show the selected transactions
                        const selectedTransactionsContainer = document.getElementById('selected-transactions-container');
                        const selectedTransactionsList = document.getElementById('selected-transactions-list');

                        if (selectedTransactionsContainer && selectedTransactionsList) {
                            // Clear the list
                            selectedTransactionsList.innerHTML = '';

                            // Add each transaction to the list
                            invoice.unit_transactions.forEach(transaction => {
                                const transactionItem = document.createElement('div');
                                transactionItem.className = 'mb-1 d-flex justify-content-between align-items-center';

                                // Get the unit code and PO number
                                const unitCode = transaction.unit ? transaction.unit.unit_code : 'N/A';
                                const poNumber = transaction.po_number || transaction.sales_notes || transaction.remarks || '-';

                                transactionItem.innerHTML = `
                                    <div>
                                        <span class="badge bg-primary">${transaction.site_id}</span>
                                        <span class="ms-2">${unitCode}</span>
                                        <span class="ms-2 text-muted small">PO: ${poNumber}</span>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-danger remove-transaction" data-id="${transaction.id}">
                                        <i class="mdi mdi-close"></i>
                                    </button>
                                `;

                                selectedTransactionsList.appendChild(transactionItem);
                            });

                            // Show the container
                            selectedTransactionsContainer.classList.remove('d-none');

                            // Add event listeners to remove buttons
                            selectedTransactionsList.querySelectorAll('.remove-transaction').forEach(button => {
                                button.addEventListener('click', function() {
                                    const transactionId = this.getAttribute('data-id');
                                    removeTransactionFromInvoice(transactionId, this.parentElement);
                                });
                            });
                        }
                    }

                    // Change the save button text
                    if (document.getElementById('save-invoice-btn')) {
                        document.getElementById('save-invoice-btn').textContent = 'Update';
                    }

                    // Show the modal and ensure values are set after modal is shown
                    $('#invoice-form-modal').modal('show');

                    // Set values again after modal is shown to ensure they're not overridden
                    $('#invoice-form-modal').on('shown.bs.modal', function() {
                        // Re-set the invoice date to ensure it's not overridden by modal scripts
                        if (document.getElementById('invoice-tanggal') && invoice.tanggal_invoice) {
                            document.getElementById('invoice-tanggal').value = formatDateForInput(invoice.tanggal_invoice);
                        }
                        // Re-set the due date as well
                        if (document.getElementById('invoice-due-date') && invoice.due_date) {
                            document.getElementById('invoice-due-date').value = formatDateForInput(invoice.due_date);
                        }

                        // Remove this event listener to prevent it from running multiple times
                        $('#invoice-form-modal').off('shown.bs.modal');
                    });
                } catch (error) {
                    console.error('Error populating form:', error);
                    Swal.fire({
                        title: "Error!",
                        text: "Terjadi kesalahan saat memuat detail invoice. Silakan coba lagi.",
                        icon: "error",
                        confirmButtonText: "OK"
                    });
                }
            })
            .catch(error => {
                // Close loading indicator
                Swal.close();

                // Log the error for debugging
                console.error('Error fetching invoice details:', error);

                // Show error message with more details
                Swal.fire({
                    title: "Error!",
                    text: `Gagal memuat detail invoice. Silakan coba lagi. (${error.message})`,
                    icon: "error",
                    confirmButtonText: "OK"
                });
            });
    }

    // Function to remove a transaction from an invoice during editing
    function removeTransactionFromInvoice(transactionId, elementToRemove) {
        // Get current transaction IDs
        const transactionIdsField = document.getElementById('invoice-transaction-ids');
        let transactionIds = JSON.parse(transactionIdsField.value || '[]');

        // Check if this is the last transaction
        if (transactionIds.length <= 1) {
            // Show warning and prevent removal
            Swal.fire({
                title: "Peringatan!",
                text: "Invoice harus memiliki minimal satu unit. Tidak dapat menghapus semua unit.",
                icon: "warning",
                confirmButtonText: "OK"
            });
            return; // Prevent removal
        }

        // Remove the transaction ID
        transactionIds = transactionIds.filter(id => id != transactionId);

        // Update the hidden field
        transactionIdsField.value = JSON.stringify(transactionIds);

        // Remove the element from the UI
        if (elementToRemove) {
            elementToRemove.remove();
        }
    }

    // Function to view invoice details
    function viewInvoiceDetails(invoiceId) {
        fetch(`/sales/invoices/${invoiceId}`)
            .then((response) => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then((invoice) => {
                // Show invoice details in a modal
                const modal = document.getElementById('transaction-details-modal');
                const modalTitle = modal.querySelector('.modal-title');
                const modalBody = modal.querySelector('.modal-body');

                // Set modal title
                modalTitle.textContent = `Invoice Details: ${invoice.no_invoice}`;

                // Format invoice date
                const invoiceDate = invoice.tanggal_invoice
                    ? new Date(invoice.tanggal_invoice)
                    : null;
                const formattedInvoiceDate = invoiceDate ? formatDate(invoiceDate) : "-";

                // Format payment date
                const paymentDate = invoice.payment_date
                    ? new Date(invoice.payment_date)
                    : null;
                const formattedPaymentDate = paymentDate ? formatDate(paymentDate) : "-";

                // Format monetary values - use calculated values if available
                const subtotal = invoice.calculated_subtotal || invoice.subtotal;
                const taxAmount = invoice.calculated_tax || invoice.tax_amount;
                const totalAmount = invoice.calculated_total || invoice.total_amount;

                const formattedSubtotal = formatRupiah(subtotal);
                const formattedTax = formatRupiah(taxAmount);
                const formattedTotal = formatRupiah(totalAmount);

                // Determine invoice status class
                let statusClass = 'bg-secondary';
                if (invoice.invoice_status === 'Lunas') {
                    statusClass = 'bg-success';
                } else if (invoice.invoice_status === 'Jatuh Tempo') {
                    statusClass = 'bg-danger';
                } else if (invoice.invoice_status === 'Belum Lunas') {
                    statusClass = 'bg-warning';
                }

                // Build HTML for invoice details
                let html = `
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h5>Invoice Information</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th>Invoice Number</th>
                                    <td>${invoice.no_invoice || "-"}</td>
                                </tr>
                                <tr>
                                    <th>Date</th>
                                    <td>${formattedInvoiceDate}</td>
                                </tr>
                                <tr>
                                    <th>Customer</th>
                                    <td>${invoice.customer || "-"}</td>
                                </tr>
                                <tr>
                                    <th>Serial Number</th>
                                    <td>${invoice.sn || "-"}</td>
                                </tr>
                                <tr>
                                    <th>Location</th>
                                    <td>${invoice.location || "-"}</td>
                                </tr>
                                <tr>
                                    <th>Trouble</th>
                                    <td>${invoice.trouble || "-"}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Payment Information</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th>Subtotal</th>
                                    <td>${formattedSubtotal}</td>
                                </tr>
                                <tr>
                                    <th>Tax (${(invoice.ppn * 100).toFixed(0)}%)</th>
                                    <td>${formattedTax}</td>
                                </tr>
                                <tr>
                                    <th>Total</th>
                                    <td>${formattedTotal}</td>
                                </tr>
                                <tr>
                                    <th>Invoice Status</th>
                                    <td><span class="badge ${statusClass}">${invoice.invoice_status}</span></td>
                                </tr>
                                <tr>
                                    <th>Payment Status</th>
                                    <td>
                                        <button class="btn btn-sm btn-${invoice.payment_status === 'Lunas' ? 'success' : 'warning'} btn-payment-status" data-id="${invoice.id}" data-status="${invoice.payment_status}">
                                            ${invoice.payment_status}
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Payment Date</th>
                                    <td>${formattedPaymentDate}</td>
                                </tr>
                                <tr>
                                    <th>Payment Notes</th>
                                    <td>${invoice.payment_notes || "-"}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                `;

                // Add unit transactions section
                html += `
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <h5>Unit Transactions</h5>
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>Site</th>
                                        <th>Unit</th>
                                        <th>PO Number</th>
                                        <th>Status</th>
                                        <th>Created At</th>
                                        <th>Updated At</th>
                                    </tr>
                                </thead>
                                <tbody>
                `;

                if (invoice.unit_transactions && invoice.unit_transactions.length > 0) {
                    invoice.unit_transactions.forEach((transaction, index) => {
                        // Format dates
                        const createdAt = new Date(transaction.created_at);
                        const updatedAt = new Date(transaction.updated_at);
                        const formattedCreatedAt = formatDate(createdAt);
                        const formattedUpdatedAt = formatDate(updatedAt);

                        // Get unit information
                        const unitInfo = transaction.unit
                            ? `${transaction.unit.unit_code} - ${transaction.unit.unit_type}`
                            : "N/A";

                        // Determine status class
                        let transactionStatusClass = 'bg-secondary';
                        if (transaction.status === 'Selesai') {
                            transactionStatusClass = 'bg-success';
                        } else if (transaction.status === 'Pending') {
                            transactionStatusClass = 'bg-warning';
                        } else if (transaction.status === 'Perbaikan') {
                            transactionStatusClass = 'bg-danger';
                        } else if (transaction.status === 'Ready PO') {
                            transactionStatusClass = 'bg-primary';
                        }

                        html += `
                            <tr>
                                <td>${index + 1}</td>
                                <td>${transaction.site ? transaction.site.site_name : transaction.site_id}</td>
                                <td>${unitInfo}</td>
                                <td>${transaction.po_number || "-"}</td>
                                <td><span class="badge ${transactionStatusClass}">${transaction.status}</span></td>
                                <td>${formattedCreatedAt}</td>
                                <td>${formattedUpdatedAt}</td>
                            </tr>
                        `;
                    });
                } else {
                    html += `<tr><td colspan="7" class="text-center">No unit transactions found</td></tr>`;
                }

                html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;

                // Set modal body content
                modalBody.innerHTML = html;

                // Add event listener to payment status button
                modalBody.querySelector('.btn-payment-status').addEventListener('click', function() {
                    const invoiceId = this.getAttribute('data-id');
                    const currentStatus = this.getAttribute('data-status');
                    showPaymentStatusModal(invoiceId, currentStatus);
                });

                // Show the modal
                const bsModal = new bootstrap.Modal(modal);
                bsModal.show();
            })
            .catch((error) => {
                Swal.fire({
                    title: "Error!",
                    text: "Gagal memuat detail invoice. Silakan coba lagi.",
                    icon: "error",
                    confirmButtonText: "OK",
                });
            });
    }

    // Function to show payment status modal
    function showPaymentStatusModal(invoiceId, currentStatus) {
        // Set invoice ID in the form
        document.getElementById('invoice-id').value = invoiceId;

        // Set current payment status
        document.getElementById('payment-status').value = currentStatus;

        // Get document upload container
        const documentUploadContainer = document.getElementById('document-upload-container');
        const documentPreview = document.getElementById('document-preview');
        const currentDocumentName = document.getElementById('current-document-name');
        const viewCurrentDocument = document.getElementById('view-current-document');

        // Helper function to format date for input without timezone issues
        function formatDateForInput(dateString) {
            if (!dateString) return '';

            // If it's already in YYYY-MM-DD format, return as is
            if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
                return dateString;
            }

            // Handle different date formats from server
            let cleanDateString = dateString;

            if (dateString.includes('T')) {
                // ISO format: YYYY-MM-DDTHH:MM:SS.000000Z
                cleanDateString = dateString.split('T')[0];
            } else if (dateString.includes(' ')) {
                // MySQL format: YYYY-MM-DD HH:MM:SS
                cleanDateString = dateString.split(' ')[0];
            }

            return cleanDateString;
        }

        // Set default payment date to today if not already paid
        if (currentStatus !== 'Lunas') {
            const today = new Date();
            const formattedToday = today.toISOString().split('T')[0];
            document.getElementById('payment-date').value = formattedToday;
        }

        // Fetch current invoice data
        fetch(`/sales/invoices/${invoiceId}`)
            .then(response => response.json())
            .then(invoice => {
                if (invoice.payment_date) {
                    document.getElementById('payment-date').value = formatDateForInput(invoice.payment_date);
                }
                if (invoice.payment_notes) {
                    document.getElementById('payment-notes').value = invoice.payment_notes;
                }

                // Show current document if exists
                if (invoice.document_path) {
                    documentPreview.classList.remove('d-none');
                    currentDocumentName.textContent = invoice.document_path.split('/').pop();
                    viewCurrentDocument.href = `/assets/invoice_documents/${invoice.document_path}`;
                } else {
                    documentPreview.classList.add('d-none');
                    currentDocumentName.textContent = '';
                }

                // Show document upload field if status is changing to Lunas
                const paymentStatusSelect = document.getElementById('payment-status');

                // Add event listener to payment status select
                paymentStatusSelect.addEventListener('change', function() {
                    if (this.value === 'Lunas' && !invoice.document_path) {
                        documentUploadContainer.classList.remove('d-none');
                        document.getElementById('document').setAttribute('required', 'required');
                    } else {
                        documentUploadContainer.classList.remove('d-none');
                        document.getElementById('document').removeAttribute('required');
                    }
                });

                // Trigger change event to set initial state
                paymentStatusSelect.dispatchEvent(new Event('change'));
            });

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('payment-status-modal'));
        modal.show();
    }

    // Add event listener to save payment status button
    document.getElementById('save-payment-status-btn').addEventListener('click', function() {
        // Get form data
        const invoiceId = document.getElementById('invoice-id').value;
        const paymentStatus = document.getElementById('payment-status').value;
        const paymentDate = document.getElementById('payment-date').value;
        const paymentNotes = document.getElementById('payment-notes').value;
        const documentFile = document.getElementById('document').files[0];

        // Validate form
        if (!paymentStatus) {
            Swal.fire({
                title: "Error!",
                text: "Status pembayaran harus dipilih",
                icon: "error",
                confirmButtonText: "OK"
            });
            return;
        }

        if (paymentStatus === 'Lunas' && !paymentDate) {
            Swal.fire({
                title: "Error!",
                text: "Tanggal pembayaran harus diisi jika status Lunas",
                icon: "error",
                confirmButtonText: "OK"
            });
            return;
        }

        // Check if document is required but not provided
        if (paymentStatus === 'Lunas' && document.getElementById('document').hasAttribute('required') && !documentFile) {
            Swal.fire({
                title: "Error!",
                text: "Dokumen invoice asli harus dilampirkan saat mengubah status menjadi Lunas",
                icon: "error",
                confirmButtonText: "OK"
            });
            return;
        }

        // Show loading indicator
        Swal.fire({
            title: 'Memproses...',
            text: 'Sedang memperbarui status pembayaran',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // Create FormData object for file upload
        const formData = new FormData();
        formData.append('invoice_id', invoiceId);
        formData.append('payment_status', paymentStatus);
        formData.append('payment_date', paymentDate);
        formData.append('payment_notes', paymentNotes);

        // Add document file if exists
        if (documentFile) {
            formData.append('document', documentFile);
        }

        // Send request to update payment status
        fetch('/sales/invoices/payment-status', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Close loading indicator
            Swal.close();
            if (data.success) {
                // Hide the modal
                try {
                    const modalElement = document.getElementById('payment-status-modal');
                    const modalInstance = bootstrap.Modal.getInstance(modalElement);
                    if (modalInstance) {
                        modalInstance.hide();
                    } else {
                        // If the instance doesn't exist, try to create a new one and hide it
                        const newModal = new bootstrap.Modal(modalElement);
                        newModal.hide();
                    }
                } catch (error) {
                    try {
                        $('#payment-status-modal').modal('hide');
                    } catch (jqError) {
                        console.error('Error closing modal with jQuery:', jqError);
                    }
                }

                // Show success message
                Swal.fire({
                    title: "Sukses!",
                    text: "Status pembayaran berhasil diperbarui",
                    icon: "success",
                    confirmButtonText: "OK"
                }).then(() => {
                    // Reload invoiced transactions
                    loadInvoicedTransactions();
                });
            } else {
                Swal.fire({
                    title: "Error!",
                    text: data.message || "Terjadi kesalahan saat memperbarui status pembayaran",
                    icon: "error",
                    confirmButtonText: "OK"
                });
            }
        })
        .catch(error => {
            // Close loading indicator if it's still open
            Swal.close();

            // Try to close the modal despite the error
            try {
                const modalElement = document.getElementById('payment-status-modal');
                const modalInstance = bootstrap.Modal.getInstance(modalElement);
                if (modalInstance) {
                    modalInstance.hide();
                }
            } catch (modalError) {
            }

            // Show error message
            Swal.fire({
                title: "Error!",
                text: "Terjadi kesalahan saat memperbarui status pembayaran, tetapi data mungkin sudah tersimpan. Silakan refresh halaman.",
                icon: "error",
                confirmButtonText: "OK"
            }).then(() => {
                // Reload the data to show the latest status
                loadInvoicedTransactions();
            });
        });
    });

    // Function to render pagination for invoiced transactions
    function renderInvoicedPagination(data) {
        if (!invoicedPaginationContainer) return;

        invoicedPaginationContainer.innerHTML = "";

        // Check if data has the expected pagination properties
        if (!data || typeof data.last_page === 'undefined' || data.last_page <= 1) {
            return;
        }

        const ul = document.createElement("ul");
        ul.className = "pagination pagination-rounded justify-content-end mb-0";

        // Previous page link
        const prevLi = document.createElement("li");
        prevLi.className = `page-item ${
            data.current_page === 1 ? "disabled" : ""
        }`;
        prevLi.innerHTML = `<a class="page-link" href="javascript:void(0);" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a>`;
        if (data.current_page > 1) {
            prevLi
                .querySelector("a")
                .addEventListener("click", () =>
                    loadInvoicedTransactions(data.current_page - 1, currentSortField, currentSortOrder)
                );
        }
        ul.appendChild(prevLi);

        // Page links
        const startPage = Math.max(1, data.current_page - 2);
        const endPage = Math.min(data.last_page, data.current_page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const li = document.createElement("li");
            li.className = `page-item ${
                i === data.current_page ? "active" : ""
            }`;
            li.innerHTML = `<a class="page-link" href="javascript:void(0);">${i}</a>`;
            if (i !== data.current_page) {
                li.querySelector("a").addEventListener("click", () =>
                    loadInvoicedTransactions(i, currentSortField, currentSortOrder)
                );
            }
            ul.appendChild(li);
        }

        // Next page link
        const nextLi = document.createElement("li");
        nextLi.className = `page-item ${
            data.current_page === data.last_page ? "disabled" : ""
        }`;
        nextLi.innerHTML = `<a class="page-link" href="javascript:void(0);" aria-label="Next"><span aria-hidden="true">&raquo;</span></a>`;
        if (data.current_page < data.last_page) {
            nextLi
                .querySelector("a")
                .addEventListener("click", () =>
                    loadInvoicedTransactions(data.current_page + 1, currentSortField, currentSortOrder)
                );
        }
        ul.appendChild(nextLi);

        invoicedPaginationContainer.appendChild(ul);
    }

    // Function to update invoiced entries info
    function updateInvoicedEntriesInfo(data) {
        if (!invoicedEntriesInfo) return;

        // Check if data has the expected pagination properties
        if (!data || !data.total || data.total === 0) {
            invoicedEntriesInfo.textContent = "Showing 0 to 0 of 0 entries";
            return;
        }

        // Make sure we have all the required properties
        if (!data.current_page || !data.per_page) {
            invoicedEntriesInfo.textContent = `Showing 1 to ${data.total} of ${data.total} entries`;
            return;
        }

        const start = (data.current_page - 1) * data.per_page + 1;
        const end = Math.min(data.current_page * data.per_page, data.total);
        invoicedEntriesInfo.textContent = `Showing ${start} to ${end} of ${data.total} entries`;
    }

    // Function to close status update form
    function closeStatusUpdateForm() {
        const statusUpdateContainer = document.getElementById('status-update-container');
        const toggleStatusFormBtn = document.getElementById('btn-toggle-status-form');
        const overlay = document.getElementById('status-form-overlay');

        if (statusUpdateContainer) {
            statusUpdateContainer.style.display = 'none';
        }

        if (toggleStatusFormBtn) {
            toggleStatusFormBtn.innerHTML = '<i class="mdi mdi-pencil"></i> Ubah Status';
            toggleStatusFormBtn.classList.remove('btn-danger');
            toggleStatusFormBtn.classList.add('btn-light');
        }

        if (overlay) {
            document.body.removeChild(overlay);
        }
    }

    // Function to close notes update form
    function closeNotesUpdateForm() {
        const notesUpdateContainer = document.getElementById('notes-update-container');
        const toggleNotesFormBtn = document.getElementById('btn-toggle-notes-form');
        const overlay = document.getElementById('notes-form-overlay');

        if (notesUpdateContainer) {
            notesUpdateContainer.style.display = 'none';
        }

        if (toggleNotesFormBtn) {
            toggleNotesFormBtn.innerHTML = '<i class="mdi mdi-note-edit"></i> Catatan Sales';
            toggleNotesFormBtn.classList.remove('btn-danger');
            toggleNotesFormBtn.classList.add('btn-info');
        }

        if (overlay) {
            document.body.removeChild(overlay);
        }
    }

    // Function to update transaction notes
    function updateTransactionNotes() {
        const transactionId = document.getElementById('notes-transaction-id').value;
        const salesNotes = document.getElementById('notes_sales_notes').value;

        // Create form data
        const formData = new FormData();
        formData.append('sales_notes', salesNotes);
        formData.append('_method', 'PUT'); // For Laravel method spoofing

        // Send request to update notes
        fetch(`/sales/transactions/${transactionId}/notes`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Close the form
                closeNotesUpdateForm();

                // Update the displayed notes in the details view
                document.getElementById('detail-sales-notes').textContent = salesNotes || '-';

                // Show success message
                Swal.fire({
                    title: "Berhasil!",
                    text: "Catatan Sales berhasil diperbarui",
                    icon: "success",
                    confirmButtonText: "OK"
                });
            } else {
                throw new Error(data.message || 'Terjadi kesalahan saat memperbarui catatan');
            }
        })
        .catch(error => {
            Swal.fire({
                title: "Error!",
                text: error.message || "Gagal memperbarui catatan. Silakan coba lagi.",
                icon: "error",
                confirmButtonText: "OK"
            });
        });
    }

    // Function to download invoice attachments
    function downloadInvoiceAttachments(invoiceId) {
        // Show loading indicator
        Swal.fire({
            title: 'Memproses...',
            text: 'Sedang menyiapkan file lampiran untuk diunduh',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // Open the download URL in a new tab
        const downloadUrl = `/sales/invoices/${invoiceId}/download-attachments`;
        const downloadWindow = window.open(downloadUrl, '_blank');

        // If the window was blocked, provide a direct link
        if (!downloadWindow || downloadWindow.closed || typeof downloadWindow.closed === 'undefined') {
            Swal.fire({
                title: 'Unduh Lampiran',
                html: `
                    <p>Jika unduhan tidak dimulai secara otomatis, silakan klik tombol di bawah ini:</p>
                    <a href="${downloadUrl}" class="btn btn-primary" target="_blank">
                        <i class="mdi mdi-download"></i> Unduh Lampiran
                    </a>
                `,
                icon: 'info',
                confirmButtonText: 'Tutup'
            });
        } else {
            // Close the loading indicator after a short delay
            setTimeout(() => {
                Swal.close();
            }, 1500);
        }
    }
});
