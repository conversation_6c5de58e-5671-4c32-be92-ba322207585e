[2025-05-24 07:25:51] local.INFO: Getting invoice details for ID: 27  
[2025-05-24 07:25:51] local.INFO: Invoice found: 27  
[2025-05-24 07:25:51] local.INFO: This is a direct invoice with subtotal: 1000.00  
[2025-05-24 07:25:51] local.INFO: Returning invoice details successfully  
[2025-05-24 07:30:50] local.INFO: Part name updated from '.BALT A-38' to 'BALT A-38' for part VB-A-5380-PWB. Site inventories synchronized.  
[2025-05-24 08:49:11] local.ERROR: Error previewing invoice: No transactions found for this invoice  
[2025-05-24 08:49:11] local.ERROR: #0 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(47): App\Http\Controllers\Sales\SalesDashboardController->previewInvoice('inv-34')
#1 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Route.php(266): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\Sales\SalesDashboardController), 'previewInvoice')
#2 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Route.php(212): Illuminate\Routing\Route->runController()
#3 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Router.php(808): Illuminate\Routing\Route->run()
#4 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(170): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\portalpwb\app\Http\Middleware\CheckSales.php(16): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): App\Http\Middleware\CheckSales->handle(Object(Illuminate\Http\Request), Object(Closure))
#7 C:\xampp\htdocs\portalpwb\app\Http\Middleware\EnsureLogDescriptions.php(20): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#8 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): App\Http\Middleware\EnsureLogDescriptions->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(51): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(88): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#14 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#15 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#16 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#17 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(75): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(127): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Router.php(807): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#24 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Router.php(786): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Router.php(750): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#26 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Routing\Router.php(739): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(201): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#28 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(170): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#29 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#32 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#33 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(51): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#34 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#35 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#36 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#37 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(110): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#38 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#39 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#40 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(58): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\InvokeDeferredCallbacks.php(22): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(209): Illuminate\Foundation\Http\Middleware\InvokeDeferredCallbacks->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(127): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(176): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#47 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(145): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#48 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(1220): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#49 C:\xampp\htdocs\portalpwb\public\index.php(17): Illuminate\Foundation\Application->handleRequest(Object(Illuminate\Http\Request))
#50 C:\xampp\htdocs\portalpwb\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(23): require_once('C:\\xampp\\htdocs...')
#51 {main}  
[2025-05-24 08:49:48] local.INFO: Getting invoice details for ID: 36  
[2025-05-24 08:49:48] local.INFO: Invoice found: 36  
[2025-05-24 08:49:48] local.INFO: Invoice has 2 transactions  
[2025-05-24 08:49:48] local.INFO: Returning invoice details successfully  
[2025-05-24 08:50:00] local.INFO: Getting invoice details for ID: 37  
[2025-05-24 08:50:00] local.INFO: Invoice found: 37  
[2025-05-24 08:50:00] local.INFO: Invoice has 2 transactions  
[2025-05-24 08:50:00] local.INFO: Returning invoice details successfully  
[2025-05-24 08:50:34] local.INFO: Getting invoice details for ID: 37  
[2025-05-24 08:50:34] local.INFO: Invoice found: 37  
[2025-05-24 08:50:34] local.INFO: Invoice has 2 transactions  
[2025-05-24 08:50:34] local.INFO: Returning invoice details successfully  
[2025-05-25 05:53:29] local.INFO: Part update details: {"part_name":"FREON KLEA","part_id":"2011","part_code":"FRN-KLEA-PWB","is_new_part":false,"old_quantity":1.0,"new_quantity":"1","quantity_difference":0.0,"current_stock":95.0} 
[2025-05-25 05:58:16] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'invoice_unit_transactions' already exists (Connection: mysql, SQL: create table `invoice_unit_transactions` (`id` bigint unsigned not null auto_increment primary key, `invoice_id` bigint unsigned not null, `unit_transaction_id` bigint unsigned not null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'invoice_unit_transactions' already exists (Connection: mysql, SQL: create table `invoice_unit_transactions` (`id` bigint unsigned not null auto_increment primary key, `invoice_id` bigint unsigned not null, `unit_transaction_id` bigint unsigned not null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('create table `i...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('create table `i...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('create table `i...')
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(418): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('invoice_unit_tr...', Object(Closure))
#6 C:\\xampp\\htdocs\\portalpwb\\database\\migrations\\2025_04_23_021926_create_invoice_unit_transactions_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('2025_04_23_0219...', Object(Closure))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_04_23_0219...', Object(Closure))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\xampp\\\\htdocs...', 41, false)
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\xampp\\htdocs\\portalpwb\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'invoice_unit_transactions' already exists at C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:571)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(571): PDOStatement->execute()
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `i...', Array)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('create table `i...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('create table `i...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('create table `i...')
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(418): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('invoice_unit_tr...', Object(Closure))
#8 C:\\xampp\\htdocs\\portalpwb\\database\\migrations\\2025_04_23_021926_create_invoice_unit_transactions_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('2025_04_23_0219...', Object(Closure))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_04_23_0219...', Object(Closure))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\xampp\\\\htdocs...', 41, false)
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\xampp\\htdocs\\portalpwb\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-05-25 06:19:56] local.INFO: User tika logged in successfully. employee_id: 000012, site_id: WHO  
[2025-05-25 06:20:11] local.INFO: Getting invoice details for ID: 38  
[2025-05-25 06:20:11] local.INFO: Invoice found: 38  
[2025-05-25 06:20:11] local.INFO: Invoice has 2 transactions  
[2025-05-25 06:20:11] local.INFO: Returning invoice details successfully  
[2025-05-25 06:20:25] local.INFO: Getting invoice details for ID: 39  
[2025-05-25 06:20:25] local.INFO: Invoice found: 39  
[2025-05-25 06:20:25] local.INFO: Invoice has 1 transactions  
[2025-05-25 06:20:25] local.INFO: Returning invoice details successfully  
[2025-05-25 06:20:44] local.INFO: Getting invoice details for ID: 38  
[2025-05-25 06:20:44] local.INFO: Invoice found: 38  
[2025-05-25 06:20:44] local.INFO: Invoice has 2 transactions  
[2025-05-25 06:20:44] local.INFO: Returning invoice details successfully  
[2025-05-25 06:21:03] local.INFO: Getting invoice details for ID: 38  
[2025-05-25 06:21:03] local.INFO: Invoice found: 38  
[2025-05-25 06:21:03] local.INFO: Invoice has 2 transactions  
[2025-05-25 06:21:03] local.INFO: Returning invoice details successfully  
[2025-05-25 06:22:16] local.INFO: Getting invoice details for ID: 39  
[2025-05-25 06:22:16] local.INFO: Invoice found: 39  
[2025-05-25 06:22:16] local.INFO: Invoice has 1 transactions  
[2025-05-25 06:22:16] local.INFO: Returning invoice details successfully  
[2025-05-25 06:22:21] local.INFO: Getting invoice details for ID: 39  
[2025-05-25 06:22:21] local.INFO: Invoice found: 39  
[2025-05-25 06:22:21] local.INFO: Invoice has 1 transactions  
[2025-05-25 06:22:21] local.INFO: Returning invoice details successfully  
[2025-05-25 06:24:17] local.INFO: Getting invoice details for ID: 39  
[2025-05-25 06:24:17] local.INFO: Invoice found: 39  
[2025-05-25 06:24:17] local.INFO: Invoice has 1 transactions  
[2025-05-25 06:24:17] local.INFO: Returning invoice details successfully  
